<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
      <el-form-item prop="p1" label="1、排尿困难">
        <el-radio-group v-model="form.p1">
          <el-radio label="是" border/>
          <el-radio label="否" border/>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.p1 === '是'" prop="p1Onset" label="起病时间">
        <el-date-picker
          v-model="form.p1Onset"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择起病时间"
          :editable="false"
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item v-if="form.p1 === '是'" prop="p1Trend" label="变化趋势">
          <el-radio-group v-model="form.p1Trend" class="p1-trend-group">
            <el-radio label="进行性加重" border />
            <el-radio label="缓解" border />
            <el-radio label="其他" border />
          </el-radio-group>
          <el-input
            v-if="form.p1Trend === '其他'"
            v-model="form.p1TrendOther"
            placeholder=""
            maxlength="20"
            show-word-limit
            class="p1-trend-other"
          />
      </el-form-item>
      <el-form-item v-if="form.p1 === '是'" prop="p1Etiology" label="病因诱因">
        <el-radio-group v-model="form.p1Etiology" class="p1-etiology-group">
          <el-radio label="有" border />
          <el-radio label="无" border />
        </el-radio-group>
        <el-input
          v-if="form.p1Etiology === '有'"
          v-model="form.p1EtiologyDetail"
          placeholder="病因诱因"
          maxlength="50"
          show-word-limit
          class="p1-etiology-detail"
        />
      </el-form-item>
      <el-form-item v-if="form.p1 === '是'" prop="p1Features" label="特点">
        <el-checkbox-group v-model="form.p1Features">
          <el-checkbox label="排尿等待" border />
          <el-checkbox label="排尿无力" border />
          <el-checkbox label="尿线分叉" border />
          <el-checkbox label="终末尿液滴沥" border />
          <el-checkbox label="排尿中断" border />
          <el-checkbox label="夜尿增多" border />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item prop="p2" label="2、尿频">
        <el-radio-group v-model="form.p2">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p3" label="3、尿急">
        <el-radio-group v-model="form.p3">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p4" label="4、尿痛">
        <el-radio-group v-model="form.p4">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p5" label="5、肉眼血尿">
        <el-radio-group v-model="form.p5">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p6" label="6、骨痛">
        <el-radio-group v-model="form.p6">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p7" label="7、体检发现PSA增高或影像学发现前列腺占位">
        <el-radio-group v-model="form.p7">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0158',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
      if (this.form.p1Onset === undefined) {
        this.$set(this.form, 'p1Onset', '');
      }
      if (this.form.p1Trend === undefined) {
        this.$set(this.form, 'p1Trend', '');
      }
      if (this.form.p1TrendOther === undefined) {
        this.$set(this.form, 'p1TrendOther', '');
      }
      if (this.form.p1Etiology === undefined) {
        this.$set(this.form, 'p1Etiology', '');
      }
      if (this.form.p1EtiologyDetail === undefined) {
        this.$set(this.form, 'p1EtiologyDetail', '');
      }
      if (this.form.p1Features === undefined) {
        this.$set(this.form, 'p1Features', []);
      }
    }
  },
  data() {
    const validateP1Onset = (rule, value, callback) => {
      if (this.form.p1 === '是' && !value) {
        callback(new Error('请选择起病时间'));
      } else {
        callback();
      }
    };
    const validateP1Trend = (rule, value, callback) => {
      if (this.form.p1 === '是' && !value) {
        callback(new Error('请选择变化趋势'));
      } else {
        callback();
      }
    };
    const validateP1TrendOther = (rule, value, callback) => {
      if (this.form.p1 === '是' && this.form.p1Trend === '其他' && !value) {
        callback(new Error('请填写其他变化趋势'));
      } else {
        callback();
      }
    };
    const validateP1Etiology = (rule, value, callback) => {
      if (this.form.p1 === '是' && !value) {
        callback(new Error('请选择病因诱因'));
      } else {
        callback();
      }
    };
    const validateP1EtiologyDetail = (rule, value, callback) => {
      if (this.form.p1 === '是' && this.form.p1Etiology === '有' && !value) {
        callback(new Error('请填写病因诱因详情'));
      } else {
        callback();
      }
    };
    return {
      form: {
        p1: undefined,
        p1Onset: '',
        p1Trend: '',
        p1TrendOther: '',
        p1Etiology: '',
        p1EtiologyDetail: '',
        p1Features: [],
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p1Onset: [{ validator: validateP1Onset, trigger: 'change' }],
        p1Trend: [{ validator: validateP1Trend, trigger: 'change' }],
        p1TrendOther: [{ validator: validateP1TrendOther, trigger: 'blur' }],
        p1Etiology: [{ validator: validateP1Etiology, trigger: 'change' }],
        p1EtiologyDetail: [{ validator: validateP1EtiologyDetail, trigger: 'blur' }],
        p1Features: [],
        p2: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p3: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p4: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p5: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p6: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p7: [{ required: true, message: '必须选择', trigger: 'blur' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
  watch: {
    'form.p1'(val) {
      if (val !== '是') {
        this.form.p1Onset = '';
        this.form.p1Trend = '';
        this.form.p1TrendOther = '';
        this.form.p1Etiology = '';
        this.form.p1EtiologyDetail = '';
        this.form.p1Features = [];
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['p1Onset', 'p1Trend', 'p1TrendOther', 'p1Etiology', 'p1EtiologyDetail', 'p1Features']);
        }
      }
    },
    'form.p1Trend'(val) {
      if (val !== '其他') {
        this.form.p1TrendOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p1TrendOther');
        }
      }
    },
  },
};
export const formConfig = {
  fieldList: [ {
      key:"p1",
      label:"1、您是否存在慢性病史（例高血压、糖尿病）？"
    }, {
      key:"p2",
      label:"2、您是否存在膀胱癌家族史？"
    }, {
      key:"p3",
      label:"3、您是否存在吸烟史？"
    }, {
      key:"p4",
      label:"4、您是否存在化学品的接触史？"
    } ],
};
</script>
<style scoped>
::v-deep .el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

::v-deep .el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-radio__label {
  padding-left: 0px;
}

::v-deep .el-form-item__label {
  width: 100%;
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

::v-deep .el-radio-group {
  font-size: 0;
  /* //margin-top: 7px; */
}

::v-deep .el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

.p1-trend-group {
  display: inline-flex;
  align-items: center;
}

.p1-trend-other {
  width: 200px;
}

.p1-etiology-group {
  display: inline-flex;
  align-items: center;
}

.p1-etiology-detail {
  width: 300px;
  margin-left: 10px;
}
</style>
